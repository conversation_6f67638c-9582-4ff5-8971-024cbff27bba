"""
Data Cleaning Service
Extracted from orchestrator to improve code organization and maintainability
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, List, Any, Optional
from loguru import logger


class DataCleaningService:
    """Dedicated service for data cleaning operations"""
    
    def __init__(self):
        self.logger = logger
    
    def get_dataset_path(self, pipeline_state, step_input: Dict = None) -> str:
        """Extract dataset path from pipeline state or step input"""
        dataset_path = None
        
        if pipeline_state.context.get("dataset_path"):
            dataset_path = pipeline_state.context["dataset_path"]
        elif step_input and step_input.get("dataset_path"):
            dataset_path = step_input["dataset_path"]
        elif pipeline_state.context.get("dataset_selection_result"):
            dataset_path = pipeline_state.context["dataset_selection_result"].get("dataset_path")
        
        if not dataset_path:
            raise ValueError("No dataset path available for cleaning")
        
        return dataset_path
    
    def load_dataset(self, dataset_path: str, user_query: str) -> pd.DataFrame:
        """Load dataset from path or create demo dataset"""
        if dataset_path.endswith('.csv'):
            return pd.read_csv(dataset_path)
        else:
            # For demo purposes, create a mock dataset based on user query
            return self._create_demo_dataset(user_query)
    
    def perform_llm_cleaning(self, df: pd.DataFrame, user_query: str, problem_type: str) -> Dict[str, Any]:
        """Perform LLM-powered data cleaning"""
        try:
            # Initialize LLM Data Analyst
            from mcp_server.tools.llm_data_analyst import LLMDataAnalyst
            llm_analyst = LLMDataAnalyst()
            
            original_shape = df.shape
            self.logger.info(f"Starting LLM-powered data cleaning for dataset shape: {original_shape}")
            
            # Step 1: LLM Analysis of the dataset
            llm_analysis = llm_analyst.analyze_dataset(df, user_query, problem_type)
            
            # Step 2: Generate LLM-powered cleaning strategy
            cleaning_strategy = llm_analyst.generate_cleaning_strategy(df, user_query, problem_type)
            
            # Step 3: Apply cleaning based on LLM recommendations
            df_cleaned, cleaning_actions = self._apply_llm_cleaning_strategy(df, cleaning_strategy)
            
            # Step 4: LLM-powered feature engineering
            feature_suggestions = llm_analyst.suggest_feature_engineering(df_cleaned, user_query, problem_type)
            df_final, feature_actions = self._apply_feature_engineering(df_cleaned, feature_suggestions)
            
            # Step 5: Generate LLM explanations for all actions
            all_actions = cleaning_actions + feature_actions
            llm_explanation = llm_analyst.explain_cleaning_decisions(all_actions, user_query)
            
            return {
                'df_final': df_final,
                'original_shape': original_shape,
                'llm_analysis': llm_analysis,
                'cleaning_strategy': cleaning_strategy,
                'feature_suggestions': feature_suggestions,
                'cleaning_actions': cleaning_actions,
                'feature_actions': feature_actions,
                'llm_explanation': llm_explanation
            }
            
        except Exception as e:
            self.logger.error(f"LLM cleaning failed: {e}")
            raise
    
    def save_cleaned_dataset(self, df: pd.DataFrame, original_dataset_path: str) -> str:
        """Save cleaned dataset with proper error handling"""
        try:
            cleaned_filename = os.path.basename(original_dataset_path).replace('.csv', '_cleaned.csv')
            cleaned_path = os.path.join(os.path.dirname(original_dataset_path), cleaned_filename)
            
            # Ensure directory exists
            os.makedirs(os.path.dirname(cleaned_path), exist_ok=True)
            
            # Save the cleaned dataset
            df.to_csv(cleaned_path, index=False)
            self.logger.info(f"✅ Cleaned dataset saved to: {cleaned_path}")
            return cleaned_path
            
        except Exception as save_error:
            self.logger.error(f"❌ Failed to save cleaned dataset: {save_error}")
            # Use a fallback path in the current directory
            cleaned_path = f"cleaned_dataset_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            df.to_csv(cleaned_path, index=False)
            self.logger.info(f"✅ Cleaned dataset saved to fallback location: {cleaned_path}")
            return cleaned_path
    
    def generate_statistics(self, df_original: pd.DataFrame, df_final: pd.DataFrame) -> Dict[str, Any]:
        """Generate comprehensive statistics for cleaned dataset"""
        original_shape = df_original.shape
        final_shape = df_final.shape
        
        # Numeric statistics
        numeric_stats = {}
        if len(df_final.select_dtypes(include=[np.number]).columns) > 0:
            numeric_stats = df_final.describe().round(2).to_dict()
        
        # Categorical statistics
        categorical_stats = {}
        for col in df_final.select_dtypes(include=['object', 'category']).columns:
            categorical_stats[col] = {
                "unique_values": int(df_final[col].nunique()),
                "most_frequent": str(df_final[col].mode().iloc[0]) if not df_final[col].mode().empty else "N/A",
                "frequency": int(df_final[col].value_counts().iloc[0]) if len(df_final[col].value_counts()) > 0 else 0
            }
        
        # Calculate data quality score
        quality_score = self._calculate_quality_score(df_original, df_final)
        
        return {
            "original_shape": original_shape,
            "cleaned_shape": final_shape,
            "rows_removed": original_shape[0] - final_shape[0],
            "columns_removed": original_shape[1] - final_shape[1],
            "numeric_statistics": numeric_stats,
            "categorical_statistics": categorical_stats,
            "data_quality_score": quality_score
        }
    
    def build_cleaning_result(self, cleaning_data: Dict[str, Any], stats: Dict[str, Any], 
                            cleaned_path: str) -> Dict[str, Any]:
        """Build comprehensive cleaning result"""
        return {
            **stats,
            "llm_analysis": cleaning_data['llm_analysis'],
            "cleaning_strategy": cleaning_data['cleaning_strategy'],
            "feature_engineering_suggestions": cleaning_data['feature_suggestions'],
            "cleaning_actions": cleaning_data['cleaning_actions'],
            "feature_engineering_actions": cleaning_data['feature_actions'],
            "llm_explanation": cleaning_data['llm_explanation'],
            "cleaned_data_path": cleaned_path,
            "cleaning_summary": f"LLM-powered data cleaning completed: {stats['original_shape'][0]} → {stats['cleaned_shape'][0]} rows, {stats['original_shape'][1]} → {stats['cleaned_shape'][1]} columns. Data quality score: {stats['data_quality_score']}%",
            "ai_insights": {
                "data_quality_assessment": cleaning_data['llm_analysis'].get("llm_analysis", {}).get("data_quality_assessment", "Analysis completed"),
                "key_recommendations": cleaning_data['llm_analysis'].get("recommendations", []),
                "feature_relevance": cleaning_data['llm_analysis'].get("llm_analysis", {}).get("feature_relevance", {}),
                "cleaning_rationale": cleaning_data['llm_explanation']
            },
            "next_steps": [
                "Review AI-generated cleaning rationale",
                "Validate feature engineering suggestions", 
                "Proceed with model training on cleaned data",
                "Consider additional domain-specific features"
            ]
        }
    
    def _calculate_quality_score(self, df_original: pd.DataFrame, df_final: pd.DataFrame) -> float:
        """Calculate data quality score"""
        # Simple quality score based on completeness and consistency
        original_missing = df_original.isnull().sum().sum()
        final_missing = df_final.isnull().sum().sum()
        
        total_cells_original = df_original.shape[0] * df_original.shape[1]
        total_cells_final = df_final.shape[0] * df_final.shape[1]
        
        if total_cells_final == 0:
            return 0.0
        
        # Quality improvement based on missing value reduction
        missing_improvement = max(0, (original_missing - final_missing) / max(1, original_missing))
        
        # Base quality score
        completeness = 1 - (final_missing / total_cells_final)
        
        # Combined score
        quality_score = (completeness * 0.7 + missing_improvement * 0.3) * 100
        
        return round(min(100, max(0, quality_score)), 1)
    
    def _create_demo_dataset(self, user_query: str) -> pd.DataFrame:
        """Create demo dataset based on user query (placeholder)"""
        # This would be implemented based on the user query
        # For now, return a simple demo dataset
        import numpy as np
        
        np.random.seed(42)
        return pd.DataFrame({
            'feature1': np.random.randn(100),
            'feature2': np.random.randn(100),
            'target': np.random.randn(100)
        })
    
    def _apply_llm_cleaning_strategy(self, df: pd.DataFrame, strategy: Dict) -> tuple:
        """Apply LLM cleaning strategy (placeholder)"""
        # This would implement the actual cleaning based on LLM strategy
        return df, ["mock_cleaning_action"]
    
    def _apply_feature_engineering(self, df: pd.DataFrame, suggestions: Dict) -> tuple:
        """Apply feature engineering suggestions (placeholder)"""
        # This would implement the actual feature engineering
        return df, ["mock_feature_action"]
