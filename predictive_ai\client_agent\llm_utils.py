"""
Shared LLM Utilities
Centralized LLM client management to eliminate duplicate code
"""

import os
from typing import Optional
from loguru import logger

# Try to import LLM libraries
try:
    from openai import OpenAI
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

try:
    from groq import Groq
    GROQ_AVAILABLE = True
except ImportError:
    GROQ_AVAILABLE = False


class LLMClientManager:
    """Centralized LLM client management"""
    
    _instance = None
    _openai_client = None
    _groq_client = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._initialize_clients()
        return cls._instance
    
    def _initialize_clients(self):
        """Initialize LLM clients based on available APIs"""
        
        # Initialize OpenAI
        if OPENAI_AVAILABLE and os.getenv('OPENAI_API_KEY'):
            try:
                self._openai_client = OpenAI(api_key=os.getenv('OPENAI_API_KEY').strip())
                logger.info("✅ OpenAI client initialized successfully")
            except Exception as e:
                logger.error(f"❌ Failed to initialize OpenAI client: {e}")
                self._openai_client = None
        else:
            logger.info("ℹ️ OpenAI API key not provided - OpenAI disabled")

        # Initialize Groq
        if GROQ_AVAILABLE and os.getenv('GROQ_API_KEY'):
            try:
                self._groq_client = Groq(api_key=os.getenv('GROQ_API_KEY').strip())
                logger.info("✅ Groq client initialized successfully")
            except Exception as e:
                logger.error(f"❌ Failed to initialize Groq client: {e}")
                self._groq_client = None
        else:
            logger.info("ℹ️ Groq API key not provided - Groq disabled")

        # Log available clients
        if not self._openai_client and not self._groq_client:
            logger.warning("⚠️ No LLM clients available - will use rule-based fallback")
        else:
            available_clients = []
            if self._openai_client:
                available_clients.append("OpenAI")
            if self._groq_client:
                available_clients.append("Groq")
            logger.info(f"🚀 LLM clients ready: {', '.join(available_clients)}")
    
    @property
    def openai_client(self) -> Optional[OpenAI]:
        """Get OpenAI client"""
        return self._openai_client
    
    @property
    def groq_client(self) -> Optional[Groq]:
        """Get Groq client"""
        return self._groq_client
    
    def has_llm_available(self) -> bool:
        """Check if any LLM client is available"""
        return self._openai_client is not None or self._groq_client is not None
    
    def call_llm(self, system_prompt: str, user_prompt: str, prefer_openai: bool = True) -> Optional[str]:
        """Call LLM with fallback logic"""
        try:
            if prefer_openai and self._openai_client:
                response = self._openai_client.chat.completions.create(
                    model="gpt-3.5-turbo",
                    messages=[
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": user_prompt}
                    ],
                    max_tokens=500,
                    temperature=0.7
                )
                return response.choices[0].message.content.strip()
            elif self._groq_client:
                response = self._groq_client.chat.completions.create(
                    model="llama3-8b-8192",
                    messages=[
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": user_prompt}
                    ],
                    max_tokens=500,
                    temperature=0.7
                )
                return response.choices[0].message.content.strip()
        except Exception as e:
            logger.error(f"LLM request failed: {e}")
        
        return None


# Global instance
llm_manager = LLMClientManager()
